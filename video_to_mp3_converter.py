#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频转MP3批量处理脚本
支持批量将MP4视频文件转换为MP3音频文件
"""

import os
import sys
import glob
import subprocess
from pathlib import Path
import argparse
from typing import List, Tuple

class VideoToMP3Converter:
    def __init__(self, input_dir: str = ".", output_dir: str = None):
        """
        初始化转换器
        
        Args:
            input_dir: 输入目录路径，默认为当前目录
            output_dir: 输出目录路径，默认与输入目录相同
        """
        self.input_dir = Path(input_dir).resolve()
        self.output_dir = Path(output_dir).resolve() if output_dir else self.input_dir
        self.supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']
        
    def check_ffmpeg(self) -> bool:
        """检查系统是否安装了ffmpeg"""
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False
    
    def find_video_files(self) -> List[Path]:
        """查找所有支持的视频文件"""
        video_files = []
        for ext in self.supported_formats:
            pattern = str(self.input_dir / f"*{ext}")
            video_files.extend([Path(f) for f in glob.glob(pattern, recursive=False)])
            # 同时查找大写扩展名
            pattern = str(self.input_dir / f"*{ext.upper()}")
            video_files.extend([Path(f) for f in glob.glob(pattern, recursive=False)])
        
        # 去重并排序
        video_files = list(set(video_files))
        video_files.sort()
        return video_files
    
    def convert_with_ffmpeg(self, input_file: Path, output_file: Path) -> Tuple[bool, str]:
        """使用ffmpeg进行转换"""
        try:
            cmd = [
                'ffmpeg',
                '-i', str(input_file),
                '-vn',  # 不包含视频流
                '-acodec', 'mp3',  # 音频编码器
                '-ab', '192k',  # 音频比特率
                '-ar', '44100',  # 采样率
                '-y',  # 覆盖输出文件
                str(output_file)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                return True, "转换成功"
            else:
                return False, f"ffmpeg错误: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return False, "转换超时（超过5分钟）"
        except Exception as e:
            return False, f"转换异常: {str(e)}"
    
    def convert_with_moviepy(self, input_file: Path, output_file: Path) -> Tuple[bool, str]:
        """使用moviepy进行转换（备用方案）"""
        try:
            from moviepy.editor import VideoFileClip
            
            with VideoFileClip(str(input_file)) as video:
                audio = video.audio
                if audio is None:
                    return False, "视频文件中没有音频轨道"
                
                audio.write_audiofile(str(output_file), 
                                    bitrate="192k",
                                    verbose=False, 
                                    logger=None)
                audio.close()
            
            return True, "转换成功"
            
        except ImportError:
            return False, "moviepy库未安装，请运行: pip install moviepy"
        except Exception as e:
            return False, f"moviepy转换异常: {str(e)}"
    
    def convert_single_file(self, input_file: Path) -> Tuple[bool, str]:
        """转换单个文件"""
        # 生成输出文件名
        output_file = self.output_dir / f"{input_file.stem}.mp3"
        
        # 检查输出文件是否已存在
        if output_file.exists():
            return False, f"输出文件已存在: {output_file.name}"
        
        # 检查输入文件是否存在且可读
        if not input_file.exists():
            return False, "输入文件不存在"
        
        if not os.access(input_file, os.R_OK):
            return False, "输入文件无法读取"
        
        # 尝试使用ffmpeg转换
        if self.check_ffmpeg():
            return self.convert_with_ffmpeg(input_file, output_file)
        else:
            # 备用方案：使用moviepy
            return self.convert_with_moviepy(input_file, output_file)
    
    def batch_convert(self) -> None:
        """批量转换所有视频文件"""
        print(f"正在扫描目录: {self.input_dir}")
        video_files = self.find_video_files()
        
        if not video_files:
            print("❌ 未找到任何支持的视频文件")
            print(f"支持的格式: {', '.join(self.supported_formats)}")
            return
        
        print(f"✅ 找到 {len(video_files)} 个视频文件")
        print(f"输出目录: {self.output_dir}")
        
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查转换工具
        has_ffmpeg = self.check_ffmpeg()
        print(f"转换工具: {'ffmpeg' if has_ffmpeg else 'moviepy (备用)'}")
        
        if not has_ffmpeg:
            print("⚠️  建议安装ffmpeg以获得更好的性能和兼容性")
        
        print("\n开始批量转换...")
        print("=" * 60)
        
        success_count = 0
        failed_files = []
        
        for i, video_file in enumerate(video_files, 1):
            print(f"\n[{i}/{len(video_files)}] 正在处理: {video_file.name}")
            
            success, message = self.convert_single_file(video_file)
            
            if success:
                print(f"✅ {message}")
                success_count += 1
            else:
                print(f"❌ {message}")
                failed_files.append((video_file.name, message))
        
        # 输出总结
        print("\n" + "=" * 60)
        print("批量转换完成！")
        print(f"✅ 成功转换: {success_count} 个文件")
        print(f"❌ 转换失败: {len(failed_files)} 个文件")
        
        if failed_files:
            print("\n失败文件详情:")
            for filename, error in failed_files:
                print(f"  • {filename}: {error}")

def main():
    parser = argparse.ArgumentParser(description="批量将视频文件转换为MP3音频文件")
    parser.add_argument("-i", "--input", default=".", 
                       help="输入目录路径 (默认: 当前目录)")
    parser.add_argument("-o", "--output", default=None,
                       help="输出目录路径 (默认: 与输入目录相同)")
    
    args = parser.parse_args()
    
    try:
        converter = VideoToMP3Converter(args.input, args.output)
        converter.batch_convert()
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
