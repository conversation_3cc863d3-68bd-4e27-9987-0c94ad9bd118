# 视频转MP3批量处理工具

这个工具包提供了多种方式来批量将视频文件转换为MP3音频文件。

## 功能特点

- ✅ 批量处理多个视频文件
- ✅ 自动查找目录中的所有视频文件
- ✅ 保留原始文件名，仅更改扩展名
- ✅ 实时显示处理进度
- ✅ 错误处理和跳过问题文件
- ✅ 支持多种视频格式：MP4, AVI, MOV, MKV, FLV, WMV

## 使用方法

### 方法1: Python脚本（推荐）

**前提条件：**
- 安装Python 3.6+
- 安装ffmpeg（推荐）或moviepy库

**使用方法：**
```bash
# 基本用法（处理当前目录的所有视频文件）
python video_to_mp3_converter.py

# 指定输入和输出目录
python video_to_mp3_converter.py -i "输入目录路径" -o "输出目录路径"

# 查看帮助
python video_to_mp3_converter.py --help
```

**安装依赖：**
```bash
# 如果没有ffmpeg，可以安装moviepy作为备用
pip install moviepy
```

### 方法2: Windows批处理脚本

**前提条件：**
- 安装ffmpeg并添加到系统PATH

**使用方法：**
1. 双击运行 `convert_videos.bat`
2. 脚本会自动处理当前目录中的所有视频文件

### 方法3: PowerShell脚本

**前提条件：**
- Windows PowerShell
- 安装ffmpeg并添加到系统PATH

**使用方法：**
```powershell
# 基本用法
.\Convert-VideosToMP3.ps1

# 指定输入和输出目录
.\Convert-VideosToMP3.ps1 -InputPath "C:\Videos" -OutputPath "C:\Audio"

# 查看帮助
.\Convert-VideosToMP3.ps1 -Help
```

## FFmpeg安装指南

### Windows安装FFmpeg

1. 访问 [FFmpeg官网](https://ffmpeg.org/download.html)
2. 点击"Windows"下载链接
3. 选择"Windows builds by BtbN"或"Windows builds from gyan.dev"
4. 下载最新的release版本
5. 解压到任意目录（如 `C:\ffmpeg`）
6. 将 `C:\ffmpeg\bin` 添加到系统环境变量PATH中

### 验证安装
打开命令提示符，输入：
```bash
ffmpeg -version
```
如果显示版本信息，说明安装成功。

## 输出设置

- **音频格式：** MP3
- **音频比特率：** 192kbps
- **采样率：** 44.1kHz
- **声道：** 保持原始设置

## 错误处理

脚本会自动处理以下情况：
- 跳过已存在的输出文件
- 处理文件权限问题
- 处理损坏的视频文件
- 显示详细的错误信息

## 注意事项

1. **文件覆盖：** 脚本不会覆盖已存在的MP3文件
2. **文件权限：** 确保对输入和输出目录有读写权限
3. **磁盘空间：** 确保有足够的磁盘空间存储输出文件
4. **处理时间：** 转换时间取决于视频文件大小和系统性能

## 故障排除

### 常见问题

**Q: 提示"ffmpeg: command not found"**
A: 需要安装ffmpeg并添加到系统PATH环境变量

**Q: Python脚本无法运行**
A: 确保安装了Python 3.6+，或尝试使用批处理/PowerShell脚本

**Q: 转换失败**
A: 检查视频文件是否损坏，或尝试使用其他格式的视频文件

**Q: 权限错误**
A: 以管理员身份运行脚本，或检查文件/目录权限

## 技术支持

如果遇到问题，请检查：
1. FFmpeg是否正确安装
2. 视频文件是否完整
3. 磁盘空间是否充足
4. 文件权限是否正确

## 许可证

本工具仅供学习和个人使用。请遵守相关的版权法律法规。
