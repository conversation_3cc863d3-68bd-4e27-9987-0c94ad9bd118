@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo 视频转MP3批量处理脚本
echo ========================================
echo.

:: 检查ffmpeg是否可用
where ffmpeg >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到ffmpeg
    echo.
    echo 请先安装ffmpeg:
    echo 1. 访问 https://ffmpeg.org/download.html
    echo 2. 下载Windows版本
    echo 3. 将ffmpeg.exe添加到系统PATH
    echo.
    pause
    exit /b 1
)

echo ✅ 找到ffmpeg，开始扫描视频文件...
echo.

:: 计数器
set /a total=0
set /a success=0
set /a failed=0

:: 扫描所有支持的视频文件
for %%f in (*.mp4 *.MP4 *.avi *.AVI *.mov *.MOV *.mkv *.MKV *.flv *.FLV *.wmv *.WMV) do (
    if exist "%%f" (
        set /a total+=1
    )
)

if %total% equ 0 (
    echo ❌ 当前目录中未找到任何视频文件
    echo 支持的格式: MP4, AVI, MOV, MKV, FLV, WMV
    echo.
    pause
    exit /b 1
)

echo ✅ 找到 %total% 个视频文件
echo.
echo 开始批量转换...
echo ========================================

:: 处理每个视频文件
set /a current=0
for %%f in (*.mp4 *.MP4 *.avi *.AVI *.mov *.MOV *.mkv *.MKV *.flv *.FLV *.wmv *.WMV) do (
    if exist "%%f" (
        set /a current+=1
        
        :: 获取文件名（不含扩展名）
        set "filename=%%~nf"
        set "output=!filename!.mp3"
        
        echo.
        echo [!current!/%total%] 正在处理: %%f
        
        :: 检查输出文件是否已存在
        if exist "!output!" (
            echo ❌ 跳过: !output! 已存在
            set /a failed+=1
        ) else (
            :: 使用ffmpeg转换
            ffmpeg -i "%%f" -vn -acodec mp3 -ab 192k -ar 44100 -y "!output!" >nul 2>&1
            
            if !errorlevel! equ 0 (
                echo ✅ 成功: !output!
                set /a success+=1
            ) else (
                echo ❌ 失败: %%f 转换出错
                set /a failed+=1
            )
        )
    )
)

echo.
echo ========================================
echo 批量转换完成！
echo ✅ 成功转换: %success% 个文件
echo ❌ 转换失败: %failed% 个文件
echo 📁 输出目录: %cd%
echo ========================================
echo.
pause
