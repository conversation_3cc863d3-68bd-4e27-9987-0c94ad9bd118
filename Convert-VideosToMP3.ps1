# 视频转MP3批量处理PowerShell脚本
# 支持批量将视频文件转换为MP3音频文件

param(
    [string]$InputPath = ".",
    [string]$OutputPath = "",
    [switch]$Help
)

# 显示帮助信息
if ($Help) {
    Write-Host @"
视频转MP3批量处理脚本

用法:
    .\Convert-VideosToMP3.ps1 [-InputPath <路径>] [-OutputPath <路径>] [-Help]

参数:
    -InputPath   输入目录路径 (默认: 当前目录)
    -OutputPath  输出目录路径 (默认: 与输入目录相同)
    -Help        显示此帮助信息

示例:
    .\Convert-VideosToMP3.ps1
    .\Convert-VideosToMP3.ps1 -InputPath "C:\Videos" -OutputPath "C:\Audio"

支持的视频格式: MP4, AVI, MOV, MKV, FLV, WMV
"@
    exit 0
}

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "视频转MP3批量处理脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 验证输入路径
if (-not (Test-Path $InputPath)) {
    Write-Host "❌ 错误: 输入路径不存在: $InputPath" -ForegroundColor Red
    exit 1
}

# 设置输出路径
if ([string]::IsNullOrEmpty($OutputPath)) {
    $OutputPath = $InputPath
}

# 创建输出目录（如果不存在）
if (-not (Test-Path $OutputPath)) {
    try {
        New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
        Write-Host "✅ 创建输出目录: $OutputPath" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ 错误: 无法创建输出目录: $OutputPath" -ForegroundColor Red
        exit 1
    }
}

# 检查ffmpeg是否可用
function Test-FFmpeg {
    try {
        $null = Get-Command ffmpeg -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

if (-not (Test-FFmpeg)) {
    Write-Host "❌ 错误: 未找到ffmpeg" -ForegroundColor Red
    Write-Host ""
    Write-Host "请先安装ffmpeg:" -ForegroundColor Yellow
    Write-Host "1. 访问 https://ffmpeg.org/download.html" -ForegroundColor Yellow
    Write-Host "2. 下载Windows版本" -ForegroundColor Yellow
    Write-Host "3. 将ffmpeg.exe添加到系统PATH" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "✅ 找到ffmpeg，开始扫描视频文件..." -ForegroundColor Green
Write-Host ""

# 支持的视频格式
$videoExtensions = @("*.mp4", "*.avi", "*.mov", "*.mkv", "*.flv", "*.wmv")

# 查找所有视频文件
$videoFiles = @()
foreach ($ext in $videoExtensions) {
    $videoFiles += Get-ChildItem -Path $InputPath -Filter $ext -File
}

if ($videoFiles.Count -eq 0) {
    Write-Host "❌ 当前目录中未找到任何视频文件" -ForegroundColor Red
    Write-Host "支持的格式: MP4, AVI, MOV, MKV, FLV, WMV" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "✅ 找到 $($videoFiles.Count) 个视频文件" -ForegroundColor Green
Write-Host "📁 输入目录: $InputPath" -ForegroundColor Cyan
Write-Host "📁 输出目录: $OutputPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "开始批量转换..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan

# 转换计数器
$successCount = 0
$failedCount = 0
$failedFiles = @()

# 处理每个视频文件
for ($i = 0; $i -lt $videoFiles.Count; $i++) {
    $videoFile = $videoFiles[$i]
    $currentNum = $i + 1
    
    # 生成输出文件名
    $outputFile = Join-Path $OutputPath "$($videoFile.BaseName).mp3"
    
    Write-Host ""
    Write-Host "[$currentNum/$($videoFiles.Count)] 正在处理: $($videoFile.Name)" -ForegroundColor Yellow
    
    # 检查输出文件是否已存在
    if (Test-Path $outputFile) {
        Write-Host "❌ 跳过: $($videoFile.BaseName).mp3 已存在" -ForegroundColor Red
        $failedCount++
        $failedFiles += @{File = $videoFile.Name; Error = "输出文件已存在"}
        continue
    }
    
    try {
        # 使用ffmpeg转换
        $ffmpegArgs = @(
            "-i", $videoFile.FullName,
            "-vn",
            "-acodec", "mp3",
            "-ab", "192k",
            "-ar", "44100",
            "-y",
            $outputFile
        )
        
        $process = Start-Process -FilePath "ffmpeg" -ArgumentList $ffmpegArgs -Wait -NoNewWindow -PassThru -RedirectStandardError "nul"
        
        if ($process.ExitCode -eq 0) {
            Write-Host "✅ 成功: $($videoFile.BaseName).mp3" -ForegroundColor Green
            $successCount++
        }
        else {
            Write-Host "❌ 失败: $($videoFile.Name) 转换出错" -ForegroundColor Red
            $failedCount++
            $failedFiles += @{File = $videoFile.Name; Error = "ffmpeg转换失败"}
        }
    }
    catch {
        Write-Host "❌ 异常: $($videoFile.Name) - $($_.Exception.Message)" -ForegroundColor Red
        $failedCount++
        $failedFiles += @{File = $videoFile.Name; Error = $_.Exception.Message}
    }
}

# 输出总结
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "批量转换完成！" -ForegroundColor Green
Write-Host "✅ 成功转换: $successCount 个文件" -ForegroundColor Green
Write-Host "❌ 转换失败: $failedCount 个文件" -ForegroundColor Red

if ($failedFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "失败文件详情:" -ForegroundColor Yellow
    foreach ($failed in $failedFiles) {
        Write-Host "  • $($failed.File): $($failed.Error)" -ForegroundColor Red
    }
}

Write-Host "📁 输出目录: $OutputPath" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Read-Host "按任意键退出"
